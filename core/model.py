
from django.db import models
from django.utils import timezone


# 软删除管理器
class SoftDeleteManager(models.Manager):
    """只返回未删除的对象"""
    def get_queryset(self):
        return super().get_queryset().filter(deleted_at__isnull=True)


# 包含软删除对象的管理器
class AllObjectsManager(models.Manager):
    """返回所有对象，包括已删除的"""
    def get_queryset(self):
        return super().get_queryset()


# 基础模型
class BaseModel(models.Model):
    # 创建时间
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    # 更新时间
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    # 软删除时间
    deleted_at = models.DateTimeField(null=True, blank=True, verbose_name="删除时间")

    # 默认管理器（只返回未删除的对象）
    objects = SoftDeleteManager()
    # 包含已删除对象的管理器
    all_objects = AllObjectsManager()

    class Meta:
        abstract = True

    def soft_delete(self):
        """软删除对象"""
        self.deleted_at = timezone.now()
        self.save(update_fields=['deleted_at'])

    def restore(self):
        """恢复已删除的对象"""
        self.deleted_at = None
        self.save(update_fields=['deleted_at'])

    def is_deleted(self):
        """检查对象是否已被软删除"""
        return self.deleted_at is not None

    @property
    def is_active_record(self):
        """检查记录是否处于活跃状态（未删除）"""
        return not self.is_deleted()